'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthStore, useAppStore } from '@/lib/stores';
import { userSignIn, userSignUp, ldapUserSignIn } from '@/lib/api/auth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Spinner } from '@/components/common';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { APP_NAME } from '@/lib/constants';

type AuthMode = 'signin' | 'signup' | 'ldap';

export default function AuthPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, isLoading, login, setLoading, setError } = useAuthStore();
  const { config } = useAppStore();

  const [mode, setMode] = useState<AuthMode>('signin');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [ldapUsername, setLdapUsername] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loaded, setLoaded] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Redirect if already authenticated
  useEffect(() => {
    if (user && !isLoading) {
      const redirectPath = searchParams.get('redirect') || '/';
      router.push(redirectPath);
    }
  }, [user, isLoading, router, searchParams]);

  // Set loaded state
  useEffect(() => {
    setLoaded(true);
  }, []);

  const handleSignIn = async () => {
    if (!email || !password) {
      toast.error('Please fill in all fields');
      return;
    }

    setIsSubmitting(true);
    setLoading(true);
    try {
      console.log('Auth page: Signing in user...');
      const response = await userSignIn(email, password);
      login(response.user, response.token);
      toast.success('Successfully signed in');
      const redirectPath = searchParams.get('redirect') || '/';
      router.push(redirectPath);
    } catch (error: any) {
      const errorMessage = error.message || 'Sign in failed';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
      setLoading(false);
    }
  };

  const handleSignUp = async () => {
    if (!name || !email || !password) {
      toast.error('Please fill in all fields');
      return;
    }

    setIsSubmitting(true);
    setLoading(true);
    try {
      console.log('Auth page: Signing up user...');
      const response = await userSignUp(name, email, password);
      login(response.user, response.token);
      toast.success('Successfully signed up');
      const redirectPath = searchParams.get('redirect') || '/';
      router.push(redirectPath);
    } catch (error: any) {
      const errorMessage = error.message || 'Sign up failed';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
      setLoading(false);
    }
  };

  const handleLdapSignIn = async () => {
    if (!ldapUsername || !password) {
      toast.error('Please fill in all fields');
      return;
    }

    setIsSubmitting(true);
    setLoading(true);
    try {
      console.log('Auth page: LDAP signing in user...');
      const response = await ldapUserSignIn(ldapUsername, password);
      login(response.user, response.token);
      toast.success('Successfully signed in');
      const redirectPath = searchParams.get('redirect') || '/';
      router.push(redirectPath);
    } catch (error: any) {
      const errorMessage = error.message || 'LDAP sign in failed';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (mode === 'ldap') {
      await handleLdapSignIn();
    } else if (mode === 'signin') {
      await handleSignIn();
    } else {
      await handleSignUp();
    }
  };

  const handleOAuthSignIn = (provider: string) => {
    // Redirect to OAuth provider
    const oauthProvider = config?.oauth?.providers?.find(p => p.name.toLowerCase() === provider.toLowerCase());
    if (oauthProvider) {
      window.location.href = `/api/auth/oauth/${provider}`;
    } else {
      toast.error(`${provider} sign-in is not configured`);
    }
  };

  if (!loaded || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <Spinner className="size-8" />
      </div>
    );
  }

  // Don't render if user is already authenticated
  if (user) {
    return null;
  }

  return (
    <div
      className="min-h-screen w-full flex items-center justify-center py-4 px-8"
      style={{ backgroundColor: '#F5F7FB' }}
    >
      <div className="flex flex-col items-center gap-6 w-full max-w-lg">
        {/* Header with Logo and Title */}
        <div className="flex flex-col items-center gap-3 text-center" style={{ width: '160px' }}>
          <div className="flex items-center justify-center mb-2">
            <img
              src="/static/logo.svg"
              alt="K2 Logo"
              className="object-contain"
              style={{ width: '68px', height: '80px' }}
              onError={(e) => {
                // Fallback if logo doesn't exist
                e.currentTarget.style.display = 'none';
                const fallback = e.currentTarget.nextElementSibling as HTMLElement;
                if (fallback) {
                  fallback.classList.remove('hidden');
                  fallback.classList.add('flex');
                }
              }}
            />
            <div className="hidden w-[68px] h-20 bg-blue-500 rounded-lg items-center justify-center text-2xl font-bold text-white">
              K2
            </div>
          </div>
          <h1
            className="text-black m-0 text-center"
            style={{
              fontFamily: 'Helvetica, Arial, sans-serif',
              fontWeight: 700,
              fontSize: '26px',
              lineHeight: '1.15'
            }}
          >
            Sign in to K2
          </h1>
        </div>

        {/* Main Form Card */}
        <div
          className="bg-white rounded-[24px] border border-[rgba(210,220,225,0.5)] shadow-[0_4px_12px_rgba(22,22,22,0.08)] flex justify-center items-center w-full"
          style={{ padding: '32px 40px', maxWidth: '560px' }}
        >
          <div className="w-full" style={{ maxWidth: '438px' }}>

              {/* Form Fields */}
              <div className="flex flex-col gap-4 w-full">
                {mode === 'signup' && (
                  <div className="flex flex-col gap-2 w-full">
                    <label
                      htmlFor="name"
                      className="text-black"
                      style={{
                        fontFamily: 'Helvetica Neue, Arial, sans-serif',
                        fontWeight: 500,
                        fontSize: '14px',
                        lineHeight: '1.14',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Name
                    </label>
                    <Input
                      id="name"
                      type="text"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      placeholder="Enter Your Full Name"
                      required
                      disabled={isSubmitting}
                      className="w-full bg-white border-2 border-[#CBD2E0] rounded-md px-3 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      style={{ height: '48px' }}
                    />
                  </div>
                )}

                {mode === 'ldap' ? (
                  <div className="flex flex-col gap-2 w-full">
                    <label
                      htmlFor="ldap-username"
                      className="text-black"
                      style={{
                        fontFamily: 'Helvetica Neue, Arial, sans-serif',
                        fontWeight: 500,
                        fontSize: '14px',
                        lineHeight: '1.14',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Username
                    </label>
                    <Input
                      id="ldap-username"
                      type="text"
                      value={ldapUsername}
                      onChange={(e) => setLdapUsername(e.target.value)}
                      placeholder="Enter your username"
                      required
                      disabled={isSubmitting}
                      className="w-full bg-white border-2 border-[#CBD2E0] rounded-md px-3 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      style={{ height: '48px' }}
                    />
                  </div>
                ) : (
                  <div className="flex flex-col gap-2 w-full">
                    <label
                      htmlFor="email"
                      className="text-black"
                      style={{
                        fontFamily: 'Helvetica Neue, Arial, sans-serif',
                        fontWeight: 500,
                        fontSize: '14px',
                        lineHeight: '1.14',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Email
                    </label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email"
                      required
                      disabled={isSubmitting}
                      className="w-full bg-white border-2 border-[#CBD2E0] rounded-md px-3 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      style={{ height: '48px' }}
                    />
                  </div>
                )}

                <div className="flex flex-col gap-2 w-full">
                  <label
                    htmlFor="password"
                    className="text-black"
                    style={{
                      fontFamily: 'Helvetica Neue, Arial, sans-serif',
                      fontWeight: 500,
                      fontSize: '14px',
                      lineHeight: '1.14',
                      letterSpacing: '-0.01em'
                    }}
                  >
                    Password
                  </label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Enter your password"
                      required
                      disabled={isSubmitting}
                      className="w-full bg-white border-2 border-[#CBD2E0] rounded-md px-3 py-3 pr-10 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      style={{ height: '48px' }}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showPassword ? (
                        // Eye-off icon (password visible, click to hide)
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M9.88 9.88a3 3 0 1 0 4.24 4.24"/>
                          <path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 11 8 11 8a13.16 13.16 0 0 1-1.67 2.68"/>
                          <path d="M6.61 6.61A13.526 13.526 0 0 0 1 12s4 8 11 8a9.74 9.74 0 0 0 5.39-1.61"/>
                          <line x1="2" y1="2" x2="22" y2="22"/>
                        </svg>
                      ) : (
                        // Eye icon (password hidden, click to show)
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7z"/>
                          <circle cx="12" cy="12" r="3"/>
                        </svg>
                      )}
                    </button>
                  </div>
                </div>

                {/* Remember me and Forgot password */}
                <div className="flex justify-between items-center w-full">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="remember"
                      className="w-4 h-4 border-2 border-[#CBD2E0] rounded-sm bg-white focus:ring-2 focus:ring-blue-500"
                    />
                    <label
                      htmlFor="remember"
                      className="text-[#485B64]"
                      style={{
                        fontFamily: 'Helvetica, Arial, sans-serif',
                        fontWeight: 400,
                        fontSize: '16px',
                        lineHeight: '1.5',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Remember for 30 days
                    </label>
                  </div>
                  <button
                    type="button"
                    className="text-[#485B64] hover:text-gray-700"
                    style={{
                      fontFamily: 'Helvetica, Arial, sans-serif',
                      fontWeight: 400,
                      fontSize: '16px',
                      lineHeight: '1.5',
                      letterSpacing: '-0.01em'
                    }}
                  >
                    Forgot Password
                  </button>
                </div>
              </div>

              {/* Submit Button and Sign up link */}
              <div className="flex flex-col items-center gap-4 w-full mt-6">
                <Button
                  type="submit"
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="w-full text-white rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{
                    backgroundColor: '#0081FB',
                    padding: '16px 24px',
                    fontFamily: 'Inter, Arial, sans-serif',
                    fontWeight: 700,
                    fontSize: '18px',
                    lineHeight: '1.33',
                    letterSpacing: '-0.01em'
                  }}
                >
                  {isSubmitting ? (
                    <div className="flex items-center justify-center gap-2">
                      <Spinner className="size-4" />
                      <span>
                        {mode === 'signin' && 'Signing in...'}
                        {mode === 'signup' && 'Creating account...'}
                        {mode === 'ldap' && 'Signing in...'}
                      </span>
                    </div>
                  ) : (
                    <span>
                      {mode === 'signin' && 'Sign in'}
                      {mode === 'signup' && 'Create Account'}
                      {mode === 'ldap' && 'Sign in'}
                    </span>
                  )}
                </Button>

                <div className="flex flex-col items-center gap-2">
                  {mode === 'signin' && (config?.enable_signup !== false) && (
                    <button
                      type="button"
                      onClick={() => setMode('signup')}
                      className="text-[#707070] hover:text-gray-600"
                      style={{
                        fontFamily: 'Helvetica, Arial, sans-serif',
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.14',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Don't have an account? Sign up
                    </button>
                  )}
                  
                  {mode === 'signup' && (
                    <button
                      type="button"
                      onClick={() => setMode('signin')}
                      className="text-[#707070] hover:text-gray-600"
                      style={{
                        fontFamily: 'Helvetica, Arial, sans-serif',
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.14',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Already have an account? Sign in
                    </button>
                  )}

                  {config?.features?.enable_ldap && mode !== 'ldap' && (
                    <button
                      type="button"
                      onClick={() => setMode('ldap')}
                      className="text-[#707070] hover:text-gray-600"
                      style={{
                        fontFamily: 'Helvetica, Arial, sans-serif',
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.14',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Sign in with LDAP
                    </button>
                  )}

                  {mode === 'ldap' && (
                    <button
                      type="button"
                      onClick={() => setMode('signin')}
                      className="text-[#707070] hover:text-gray-600"
                      style={{
                        fontFamily: 'Helvetica, Arial, sans-serif',
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.14',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Back to email sign in
                    </button>
                  )}
                </div>
              </div>

              {/* OR Divider */}
              <div className="flex items-center gap-4 w-full my-6">
                <div className="flex-1 h-px bg-[#D2DCE1]"></div>
                <span
                  className="text-[#485B64] px-4"
                  style={{
                    fontFamily: 'Inter, Arial, sans-serif',
                    fontWeight: 600,
                    fontSize: '14px',
                    lineHeight: '1.14',
                    letterSpacing: '-0.01em'
                  }}
                >
                  OR
                </span>
                <div className="flex-1 h-px bg-[#D2DCE1]"></div>
              </div>

              {/* Social Login Buttons */}
              <div className="flex gap-4 w-full">
                {/* Google */}
                <button
                  type="button"
                  onClick={() => handleOAuthSignIn('google')}
                  disabled={isSubmitting}
                  className="flex-1 flex items-center justify-center gap-6 bg-[#EDF0F7] rounded-2xl transition-colors hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{ padding: '10px 33px', height: '64px' }}
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                    <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                    <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                    <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                  </svg>
                </button>

                {/* Apple */}
                <button
                  type="button"
                  onClick={() => handleOAuthSignIn('apple')}
                  disabled={isSubmitting}
                  className="flex-1 flex items-center justify-center gap-6 bg-[#EDF0F7] rounded-2xl transition-colors hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{ padding: '10px 33px', height: '64px' }}
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" fill="currentColor"/>
                  </svg>
                </button>

                {/* Facebook */}
                <button
                  type="button"
                  onClick={() => handleOAuthSignIn('facebook')}
                  disabled={isSubmitting}
                  className="flex-1 flex items-center justify-center gap-6 bg-[#EDF0F7] rounded-2xl transition-colors hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{ padding: '10px 33px', height: '64px' }}
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" fill="#1877F2"/>
                  </svg>
                </button>
              </div>
          </div>
        </div>

        {/* Terms and Privacy */}
        <p
          className="text-center text-[#79817D] max-w-md"
          style={{
            fontFamily: 'Inter, Arial, sans-serif',
            fontWeight: 400,
            fontSize: '12px',
            lineHeight: '1.5'
          }}
        >
          By creating an account, you agree to the Terms of Service and acknowledge that you have read and understood the Privacy Policy
        </p>
      </div>
    </div>
  );
}
