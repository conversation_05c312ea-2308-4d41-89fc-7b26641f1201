import { useCallback, useEffect } from 'react';
import { useAuthStore } from '@/lib/stores';
import { getSessionUser, userSignIn, userSignUp, userSignOut, ldapUserSignIn } from '@/lib/api/auth';
import { getFromStorage, setToStorage, removeFromStorage } from '@/lib/utils';
import { STORAGE_KEYS } from '@/lib/constants';
import { toast } from 'sonner';

export const useAuth = () => {
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    setUser,
    setToken,
    login,
    logout,
    setLoading,
    setError,
    clearError
  } = useAuthStore();

  // Initialize auth state from storage
  useEffect(() => {
    let isMounted = true; // Prevent state updates if component unmounts

    const initAuth = async () => {
      if (!isMounted) return;

      setLoading(true);
      console.log('Initializing auth from storage...');

      try {
        const storedToken = getFromStorage(STORAGE_KEYS.TOKEN, null);

        if (storedToken && isMounted) {
          console.log('Found stored token, setting token and verifying...');
          // Set token first to allow app to render
          setToken(storedToken);

          try {
            // Verify token with backend in background
            console.log('Calling getSessionUser to verify token...');
            const sessionUser = await getSessionUser(storedToken);
            if (isMounted) {
              console.log('Token verified successfully, logging in user');
              login(sessionUser, storedToken);
            }
          } catch (error) {
            console.warn('Token validation failed, but keeping token for now:', error);
            if (isMounted) {
              // Don't logout immediately - let the user continue using the app
              // The token will be cleared when they try to make authenticated requests
              setError('Authentication may have expired. Please sign in again if you encounter issues.');
            }
          }
        } else {
          console.log('No stored token found');
        }
      } catch (error) {
        console.error('Failed to initialize auth:', error);
        if (isMounted) {
          // Only clear token if there's a critical error
          removeFromStorage(STORAGE_KEYS.TOKEN);
          logout();
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    initAuth();

    return () => {
      isMounted = false;
    };
  }, []);

  // Sign in
  const signIn = useCallback(async (email: string, password: string) => {
    setLoading(true);
    clearError();

    try {
      const response = await userSignIn(email, password);
      setToStorage(STORAGE_KEYS.TOKEN, response.token);
      login(response.user, response.token);
      toast.success('Successfully signed in');
      return response;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to sign in';
      setError(errorMessage);
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [login, setLoading, setError, clearError]);

  // Sign up
  const signUp = useCallback(async (
    name: string,
    email: string,
    password: string,
    profileImageUrl?: string
  ) => {
    setLoading(true);
    clearError();

    try {
      const response = await userSignUp(name, email, password, profileImageUrl);
      setToStorage(STORAGE_KEYS.TOKEN, response.token);
      login(response.user, response.token);
      toast.success('Account created successfully');
      return response;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to create account';
      setError(errorMessage);
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [login, setLoading, setError, clearError]);

  // LDAP sign in
  const ldapSignIn = useCallback(async (email: string, password: string) => {
    setLoading(true);
    clearError();

    try {
      const response = await ldapUserSignIn(email, password);
      setToStorage(STORAGE_KEYS.TOKEN, response.token);
      login(response.user, response.token);
      toast.success('Successfully signed in with LDAP');
      return response;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to sign in with LDAP';
      setError(errorMessage);
      toast.error(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [login, setLoading, setError, clearError]);

  // Sign out
  const signOut = useCallback(async () => {
    setLoading(true);

    try {
      await userSignOut();
    } catch (error: any) {
      console.error('Failed to sign out from server:', error);
      // Don't show error to user if server signout fails
      // This is common when the API doesn't exist or is unavailable
    }
    
    // Always perform local logout regardless of server response
    removeFromStorage(STORAGE_KEYS.TOKEN);
    logout();
    toast.success('Successfully signed out');
    setLoading(false);
  }, [logout, setLoading]);

  // Refresh user session
  const refreshSession = useCallback(async () => {
    if (!token) {
      console.log('No token available for session refresh');
      return;
    }

    try {
      console.log('Refreshing user session...');
      const sessionUser = await getSessionUser(token);
      console.log('Session refreshed successfully');
      setUser(sessionUser);
      clearError(); // Clear any previous auth errors
      return sessionUser;
    } catch (error) {
      console.warn('Failed to refresh session:', error);
      // Don't logout immediately - just set an error
      // This allows ongoing chats to continue
      setError('Session refresh failed. Please sign in again if you encounter issues.');
      throw error;
    }
  }, [token, setUser, clearError, setError]);

  return {
    // State
    user,
    token,
    isAuthenticated,
    isLoading,
    error,

    // Actions
    signIn,
    signUp,
    ldapSignIn,
    signOut,
    refreshSession,
    clearError,
  };
};
