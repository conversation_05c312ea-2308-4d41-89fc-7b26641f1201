"use client";

import React, { useState, useEffect, useCallback } from "react";
import { MessageSquare, Bo<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useAppStore, useAuthStore } from "@/lib/stores";
import { useChats } from "@/hooks/useChats";
import { ChatItem } from "@/components/layout/sidebar/ChatItem";
// import type { ConversationEntry } from "@/lib/types"; // Unused for now
import {
  getChatById,
  updateChatById,
  deleteChatById,
  cloneChatById,
  shareChatById,
  archiveChatById,
  toggleChatPinnedById,
  getPinnedChatList,
  generateTitle,
  getAllTags
} from "@/lib/api";

interface ConversationListProps {
  currentConversationId?: string;
  onConversationSelect: (id: string) => void;
  onNewConversation: (type?: 'chat' | 'agent') => void;
  className?: string;
}

interface ChatListItem {
  id: string;
  title: string;
  updated_at: string;
  created_at: string;
  pinned?: boolean;
  archived?: boolean;
  tags?: string[];
  folder_id?: string;
  chat?: {
    messages?: Record<string, unknown>[];
    models?: string[];
  };
}

export const ConversationList: React.FC<ConversationListProps> = ({
  currentConversationId,
  onConversationSelect,
  onNewConversation,
  className
}) => {
  const { models } = useAppStore();
  const { token, isAuthenticated } = useAuthStore();
  const { chats, isLoading: chatsLoading } = useChats(); // Use useChats hook
  const [pinnedChats, setPinnedChats] = useState<ChatListItem[]>([]);
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [shiftKey, setShiftKey] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [tags, setTags] = useState<unknown[]>([]);
  
  // Load pinned chats and tags (chats are loaded by useChats hook)
  const loadData = useCallback(async () => {
    if (!token) return;

    setIsLoading(true);
    try {
      const [pinnedResponse, tagsResponse] = await Promise.all([
        getPinnedChatList(token),
        getAllTags(token)
      ]);

      setPinnedChats(pinnedResponse || []);
      setTags(tagsResponse || []);
    } catch (error) {
      console.error('Failed to load pinned chats and tags:', error);
    } finally {
      setIsLoading(false);
    }
  }, [token]);
  
  useEffect(() => {
    if (isAuthenticated && pinnedChats.length === 0 && tags.length === 0 && !isLoading) {
      loadData();
    }
  }, [isAuthenticated, pinnedChats.length, tags.length, isLoading]); // Load only pinned chats and tags
  
  // Keyboard event handling
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      setShiftKey(e.shiftKey);
    };
    
    const handleKeyUp = (e: KeyboardEvent) => {
      setShiftKey(e.shiftKey);
    };
    
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, []);
  
  // Chat handlers
  const handleChatRename = useCallback(async (chatId: string, newTitle: string) => {
    if (!token) return;
    
    try {
      const chat = await getChatById(token, chatId);
      if (chat) {
        const updatedChat = {
          ...chat.chat,
          title: newTitle
        };
        await updateChatById(token, chatId, updatedChat);
        await loadData(); // Refresh the list
      }
    } catch (error) {
      console.error('Failed to rename chat:', error);
    }
  }, [token, loadData]);
  
  const handleChatDelete = useCallback(async (chatId: string) => {
    if (!token) return;
    
    try {
      await deleteChatById(token, chatId);
      await loadData(); // Refresh the list
    } catch (error) {
      console.error('Failed to delete chat:', error);
    }
  }, [token, loadData]);
  
  const handleChatClone = useCallback(async (chatId: string) => {
    if (!token) return;

    try {
      const chat = chats.find(c => c.id === chatId) || pinnedChats.find(c => c.id === chatId);
      if (chat) {
        const cloneTitle = `Clone of ${chat.title}`;
        await cloneChatById(token, chatId, cloneTitle);
        await loadData(); // Refresh the list
      }
    } catch (error) {
      console.error('Failed to clone chat:', error);
    }
  }, [token, chats, pinnedChats, loadData]);
  
  const handleChatShare = useCallback(async (chatId: string) => {
    if (!token) return;
    
    try {
      const sharedChat = await shareChatById(token, chatId);
      if (sharedChat) {
        // Handle share success (maybe show a modal or copy link)
        console.log('Chat shared successfully:', sharedChat);
      }
    } catch (error) {
      console.error('Failed to share chat:', error);
    }
  }, [token]);
  
  const handleChatArchive = useCallback(async (chatId: string) => {
    if (!token) return;
    
    try {
      await archiveChatById(token, chatId);
      await loadData(); // Refresh the list
    } catch (error) {
      console.error('Failed to archive chat:', error);
    }
  }, [token, loadData]);
  
  const handleChatPin = useCallback(async (chatId: string) => {
    if (!token) return;
    
    try {
      await toggleChatPinnedById(token, chatId);
      await loadData(); // Refresh the list
    } catch (error) {
      console.error('Failed to toggle pin status:', error);
    }
  }, [token, loadData]);
  
  const handleGenerateTitle = useCallback(async (chatId: string): Promise<string | null> => {
    if (!token) return null;
    
    try {
      const chat = await getChatById(token, chatId);
      if (chat && chat.chat?.messages && models.length > 0) {
        const messages = Object.values(chat.chat.messages).map((msg: Record<string, unknown>) => ({
          role: msg.role,
          content: msg.content
        }));
        
        const model = chat.chat.models?.[0] || models[0]?.id;
        if (model) {
          return await generateTitle(token, model, messages);
        }
      }
      return null;
    } catch (error) {
      console.error('Failed to generate title:', error);
      return null;
    }
  }, [token, models]);
  
  const handleChatSelect = useCallback((chatId: string) => {
    console.log('Navigating to chat:', chatId); // Debug log
    setSelectedChat(chatId);
    onConversationSelect(chatId);
  }, [onConversationSelect]);
  
  const handleChatUnselect = useCallback(() => {
    setSelectedChat(null);
  }, []);
  
  const allChats = [...pinnedChats, ...chats];

  // These functions are no longer needed as ChatItem handles formatting

  if (isLoading || chatsLoading) {
    return (
      <div className={cn("flex flex-col h-full", className)}>
        <div className="p-4 border-b">
          <h2 className="font-semibold text-lg">Conversations</h2>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-gray-500 text-sm">Loading conversations...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header - simplified to match ifm-chat-feature-k2 */}
      <div className="p-4 border-b">
        <h2 className="font-semibold text-lg">Conversations</h2>
      </div>

      {/* Conversation List */}
      <div className="flex-1 overflow-y-auto">
        {allChats.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <div className="mb-4">
              <MessageSquare className="w-12 h-12 mx-auto text-gray-300" />
            </div>
            <p className="text-sm mb-4">No conversations yet</p>
            <div className="space-y-2">
              <Button
                onClick={() => onNewConversation('chat')}
                className="w-full"
                size="sm"
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Start Chat
              </Button>
              <Button
                onClick={() => onNewConversation('agent')}
                variant="outline"
                className="w-full"
                size="sm"
              >
                <Bot className="w-4 h-4 mr-2" />
                Start Agent Research
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-1">
            {/* Pinned Chats Section */}
            {pinnedChats.length > 0 && (
              <div className="mb-4">
                <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide px-3 py-2">
                  Pinned
                </h3>
                <div className="space-y-1">
                  {pinnedChats.map((chat) => (
                    <ChatItem
                      key={chat.id}
                      chat={chat}
                      isActive={currentConversationId === chat.id}
                      isSelected={selectedChat === chat.id}
                      shiftKey={shiftKey}
                      onSelect={() => handleChatSelect(chat.id)}
                      onUnselect={handleChatUnselect}
                      onRename={(newTitle) => handleChatRename(chat.id, newTitle)}
                      onDelete={() => handleChatDelete(chat.id)}
                      onClone={() => handleChatClone(chat.id)}
                      onShare={() => handleChatShare(chat.id)}
                      onArchive={() => handleChatArchive(chat.id)}
                      onPin={() => handleChatPin(chat.id)}
                      onGenerateTitle={() => handleGenerateTitle(chat.id)}
                      onChange={loadData}
                      draggable={true}
                      className="mx-2"
                    />
                  ))}
                </div>
              </div>
            )}
            
            {/* Regular Chats Section */}
            {chats.length > 0 && (
              <div>
                <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide px-3 py-2">
                  Recent
                </h3>
                <div className="space-y-1">
                  {chats.map((chat) => (
                    <ChatItem
                      key={chat.id}
                      chat={chat}
                      isActive={currentConversationId === chat.id}
                      isSelected={selectedChat === chat.id}
                      shiftKey={shiftKey}
                      onSelect={() => handleChatSelect(chat.id)}
                      onUnselect={handleChatUnselect}
                      onRename={(newTitle) => handleChatRename(chat.id, newTitle)}
                      onDelete={() => handleChatDelete(chat.id)}
                      onClone={() => handleChatClone(chat.id)}
                      onShare={() => handleChatShare(chat.id)}
                      onArchive={() => handleChatArchive(chat.id)}
                      onPin={() => handleChatPin(chat.id)}
                      onGenerateTitle={() => handleGenerateTitle(chat.id)}
                      onChange={loadData}
                      draggable={true}
                      className="mx-2"
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};