'use client';

import React, { useEffect, useRef } from 'react';
import { useAuth } from '@/hooks/useAuth';

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  const initializeCalledRef = useRef(false);

  // Initialize auth only once at the app level
  useEffect(() => {
    if (!initializeCalledRef.current) {
      console.log('AuthProvider: Initializing authentication...');
      initializeCalledRef.current = true;
      // The useAuth hook will handle the actual initialization
    }
  }, []);

  return <>{children}</>;
};
