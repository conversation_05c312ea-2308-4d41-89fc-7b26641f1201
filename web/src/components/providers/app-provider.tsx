'use client';

import { useEffect, useRef } from 'react';
import { useAppStore } from '@/lib/stores';

export function AppProvider({ children }: { children: React.ReactNode }) {
  const { initialize, isInitialized, isLoading } = useAppStore();
  const initializeCalledRef = useRef(false);

  useEffect(() => {
    // Prevent multiple initialization calls
    if (!isInitialized && !isLoading && !initializeCalledRef.current) {
      console.log('AppProvider: Starting app initialization');
      initializeCalledRef.current = true;
      initialize();
    }
  }, [initialize, isInitialized, isLoading]);

  return <>{children}</>;
}