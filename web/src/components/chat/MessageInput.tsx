'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/common';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { Square, ArrowUp } from 'lucide-react';
import { VoiceRecording } from './MessageInput/VoiceRecording';



const PlusIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={cn("w-5 h-5", className)}
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M12 4.5v15m7.5-7.5h-15"
    />
  </svg>
);

const MicrophoneIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={cn("w-5 h-5", className)}
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M12 18.75a6 6 0 0 0 6-6v-1.5m-6 7.5a6 6 0 0 1-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 0 1-3-3V4.5a3 3 0 1 1 6 0v8.25a3 3 0 0 1-3 3Z"
    />
  </svg>
);

interface MessageInputProps {
  prompt: string;
  onPromptChange: (prompt: string) => void;
  onSubmit: (data: any) => void;
  placeholder?: string;
  disabled?: boolean;
  transparentBackground?: boolean;
  selectedModels?: string[];
  files?: any[];
  onFilesChange?: (files: any[]) => void;
  className?: string;
  isGenerating?: boolean;
  onStop?: () => void;
}

export const MessageInput: React.FC<MessageInputProps> = ({
  prompt,
  onPromptChange,
  onSubmit,
  placeholder = "Ask K2 Anything....",
  disabled = false,
  transparentBackground = false,
  selectedModels = [],
  files = [],
  onFilesChange,
  className,
  isGenerating = false,
  onStop
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = useCallback(async () => {
    // Prevent multiple simultaneous submissions
    if (isSubmitting || disabled || isGenerating) {
      console.log('Submission blocked: isSubmitting=', isSubmitting, 'disabled=', disabled, 'isGenerating=', isGenerating);
      return;
    }

    if (!prompt.trim() && files.length === 0) {
      toast.error('Please enter a prompt');
      return;
    }

    // Check if models are selected and not empty
    if (!selectedModels.length || selectedModels.includes('')) {
      toast.error('Model not selected');
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit({
        prompt: prompt.trim(),
        files,
        selectedModels
      });

      // Clear input after successful submit
      onPromptChange('');
      if (onFilesChange) {
        onFilesChange([]);
      }
    } catch (error) {
      console.error('Failed to submit message:', error);
      // Don't clear input on error so user can retry
    } finally {
      // Reset submitting state after a short delay
      setTimeout(() => {
        setIsSubmitting(false);
      }, 500);
    }
  }, [prompt, files, selectedModels, onSubmit, onPromptChange, onFilesChange, isSubmitting, disabled, isGenerating]);

  const handleFileUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFiles = Array.from(e.target.files || []);
    
    if (uploadedFiles.length > 0) {
      const newFiles = uploadedFiles.map(file => ({
        id: Math.random().toString(36).substring(2, 11),
        name: file.name,
        type: file.type,
        size: file.size,
        file: file
      }));

      if (onFilesChange) {
        onFilesChange([...files, ...newFiles]);
      }
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [files, onFilesChange]);

  const removeFile = useCallback((fileId: string) => {
    if (onFilesChange) {
      onFilesChange(files.filter(file => file.id !== fileId));
    }
  }, [files, onFilesChange]);

  const handleVoiceRecording = useCallback(() => {
    setIsRecording(true);
  }, []);

  const handleVoiceCancel = useCallback(() => {
    setIsRecording(false);
  }, []);

  const handleVoiceConfirm = useCallback((data: { text: string; filename?: string }) => {
    const { text } = data;
    onPromptChange(prompt + text + ' ');
    setIsRecording(false);
  }, [prompt, onPromptChange]);

  return (
    <div className={cn(
      "w-full",
      transparentBackground ? "bg-transparent" : "bg-white dark:bg-gray-900",
      className
    )}>
      {/* File attachments display */}
      {files.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-3 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
          {files.map((file) => (
            <div
              key={file.id}
              className="flex items-center gap-2 bg-white dark:bg-gray-700 px-3 py-1 rounded-full text-sm"
            >
              <span className="truncate max-w-32">{file.name}</span>
              <button
                onClick={() => removeFile(file.id)}
                className="text-gray-500 hover:text-red-500 transition-colors"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Input container */}
      <div className="relative flex items-center gap-4 px-6 py-4 border border-gray-200 dark:border-gray-600 rounded-3xl bg-gray-50 dark:bg-gray-800 shadow-sm min-h-[60px]">
        {/* Add/File upload button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => fileInputRef.current?.click()}
          className="shrink-0 w-10 h-10 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full"
          title="Attach files"
        >
          <PlusIcon className="w-6 h-6" />
        </Button>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileUpload}
          className="hidden"
          accept="image/*,text/*,.pdf,.doc,.docx"
        />

        {/* Text input */}
        <div className="flex-1 min-h-[32px] max-h-[120px] overflow-y-auto flex items-center">
          <Textarea
            value={prompt}
            onChange={onPromptChange}
            placeholder={placeholder}
            className="w-full bg-transparent border-none outline-none resize-none text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 text-base leading-relaxed"
            rows={1}
            onFocus={() => {}}
            onBlur={() => {}}
          />
        </div>

        {/* Voice recording button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={handleVoiceRecording}
          className={cn(
            "shrink-0 w-10 h-10 transition-colors rounded-full hover:bg-gray-200 dark:hover:bg-gray-700",
            isRecording
              ? "text-red-500 hover:text-red-600"
              : "text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
          )}
          title="Voice input"
        >
          <MicrophoneIcon className="w-6 h-6" />
        </Button>

        {/* Send/Stop button */}
        <Button
          onClick={isGenerating ? onStop : handleSubmit}
          disabled={!isGenerating && (disabled || isSubmitting || (!prompt.trim() && files.length === 0))}
          size="icon"
          className={cn(
            "shrink-0 w-10 h-10 rounded-xl text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all",
            "bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 dark:disabled:bg-gray-600",
            isSubmitting && "opacity-75"
          )}
          title={isGenerating ? "Stop generation" : isSubmitting ? "Sending..." : "Send message"}
        >
          {isGenerating ? (
            <Square className="w-5 h-5 fill-current" />
          ) : (
            <ArrowUp className="w-5 h-5" />
          )}
        </Button>
      </div>

      {/* Voice Recording Modal */}
      {isRecording && (
        <VoiceRecording
          onCancel={handleVoiceCancel}
          onConfirm={handleVoiceConfirm}
        />
      )}
    </div>
  );
};
