'use client';

import React, { useState, useEffect } from 'react';
import { useAuthStore, useAppStore } from '@/lib/stores';
import { Tooltip } from '@/components/common';
import { MessageInput } from './MessageInput';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

// Icon components
const EyeSlashIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={2.5}
    stroke="currentColor"
    className={cn("w-5 h-5", className)}
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 1-4.243-4.243m4.242 4.242L9.88 9.88"
    />
  </svg>
);

interface SuggestionPrompt {
  title?: string[];
  content: string;
}

interface ChatPlaceholderProps {
  transparentBackground?: boolean;
  selectedModels: string[];
  prompt: string;
  onPromptChange: (prompt: string) => void;
  onSubmit: (data: any) => void;
  temporaryChatEnabled?: boolean;
  className?: string;
}

export const ChatPlaceholder: React.FC<ChatPlaceholderProps> = ({
  transparentBackground = false,
  selectedModels,
  prompt,
  onPromptChange,
  onSubmit,
  temporaryChatEnabled = false,
  className
}) => {
  const { user } = useAuthStore();
  const { config, models } = useAppStore();
  const [selectedModelIdx, setSelectedModelIdx] = useState(0);

  // Get selected model objects
  const selectedModelObjects = selectedModels.map(id => 
    models.find(m => m.id === id)
  ).filter(Boolean);

  useEffect(() => {
    if (selectedModels.length > 0) {
      setSelectedModelIdx(selectedModelObjects.length - 1);
    }
  }, [selectedModels, selectedModelObjects.length]);

  const [isProcessingSuggestion, setIsProcessingSuggestion] = useState(false);

  const selectSuggestionPrompt = async (suggestionContent: string) => {
    // Prevent multiple simultaneous suggestion selections
    if (isProcessingSuggestion) {
      console.log('Already processing a suggestion, ignoring duplicate click');
      return;
    }

    setIsProcessingSuggestion(true);

    try {
      let text = suggestionContent;

      if (suggestionContent.includes('{{CLIPBOARD}}')) {
        try {
          const clipboardText = await navigator.clipboard.readText();
          text = suggestionContent.replaceAll('{{CLIPBOARD}}', clipboardText);
        } catch (err) {
          toast.error('Failed to read clipboard contents');
          return;
        }
      }

      onPromptChange(text);

      // Focus on chat input after setting prompt
      setTimeout(() => {
        const chatInputElement = document.getElementById('chat-input');
        if (chatInputElement) {
          chatInputElement.focus();
          chatInputElement.dispatchEvent(new Event('input'));
        }
      }, 100);
    } finally {
      // Reset the processing flag after a short delay
      setTimeout(() => {
        setIsProcessingSuggestion(false);
      }, 500);
    }
  };

  const currentModel = selectedModelObjects[selectedModelIdx];
  const suggestionPrompts = currentModel?.info?.meta?.suggestion_prompts || 
                           config?.default_prompt_suggestions || 
                           [];

  const defaultSuggestions: SuggestionPrompt[] = [
    {
      title: ['Explain options trading', 'if I\'m familiar with buying and selling stocks'],
      content: 'Explain options trading if I\'m familiar with buying and selling stocks'
    },
    {
      title: ['Show me a code snippet', 'of a website\'s sticky header'],
      content: 'Show me a code snippet of a website\'s sticky header'
    },
    {
      title: ['Tell me a fun fact', 'about the Roman Empire'],
      content: 'Tell me a fun fact about the Roman Empire'
    }
  ];

  const displaySuggestions = suggestionPrompts.length > 0 ? suggestionPrompts.slice(0, 3) : defaultSuggestions;

  return (
    <div className={cn("m-auto w-full max-w-6xl px-2 2xl:px-20 translate-y-6 py-24 text-center", className)}>
      {temporaryChatEnabled && (
        <Tooltip
          content="This chat won't appear in history and your messages will not be saved."
          className="w-full flex justify-center mb-0.5"
          placement="top"
        >
          <div className="flex items-center gap-2 text-gray-500 font-medium text-lg my-2 w-fit">
            <EyeSlashIcon className="size-5" />
            Temporary Chat
          </div>
        </Tooltip>
      )}

      <div className="w-full text-3xl text-gray-800 dark:text-gray-100 text-center flex items-center gap-4 font-primary">
        <div className="w-full flex flex-col justify-center items-center">
          <div className="flex flex-row justify-center gap-3 sm:gap-3.5 w-fit px-5">
            <div 
              className="line-clamp-1 animate-fade-in" 
              style={{ color: '#0081FB', fontSize: '28px' }}
            >
              {currentModel?.name ? 
                (currentModel.name === "K2-Think" ? currentModel.name : "K2-Think") :
                `Hello, ${user?.name || 'User'}`
              }
            </div>
          </div>

          {/* "What can I help with?" text */}
          <div className="mt-4 mb-2">
            <div className="text-lg text-gray-600 dark:text-gray-400 font-normal animate-fade-in">
              What can I help with?
            </div>
          </div>

          {/* Model description */}
          {currentModel?.info?.meta?.description && (
            <div className="flex mt-1 mb-2">
              <div className="animate-fade-in">
                <Tooltip
                  className="w-fit"
                  content={currentModel.info.meta.description}
                  placement="top"
                >
                  <div className="mt-0.5 px-2 text-sm font-normal text-gray-500 dark:text-gray-400 line-clamp-2 max-w-xl">
                    {currentModel.info.meta.description}
                  </div>
                </Tooltip>

                {currentModel.info.meta.user && (
                  <div className="mt-0.5 text-sm font-normal text-gray-400 dark:text-gray-500">
                    By {currentModel.info.meta.user.name || `@${currentModel.info.meta.user.username}`}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Suggestion cards */}
          <div className="flex flex-wrap gap-4 justify-center max-w-4xl mb-8 mt-8" style={{ padding: '0px 72px' }}>
            {displaySuggestions.map((suggestion, idx) => (
              <button
                key={idx}
                className={cn(
                  "flex-1 min-w-[240px] max-w-[280px] p-4 bg-blue-50 dark:bg-blue-900/20 rounded-2xl text-left hover:shadow-md transition-shadow",
                  isProcessingSuggestion && "opacity-50 cursor-not-allowed"
                )}
                onClick={() => selectSuggestionPrompt(suggestion.content)}
                disabled={isProcessingSuggestion}
              >
                {suggestion.title && suggestion.title[0] ? (
                  <>
                    <h3 className="text-base font-medium text-black dark:text-white mb-2">
                      {suggestion.title[0]}
                    </h3>
                    {suggestion.title[1] && (
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {suggestion.title[1]}
                      </p>
                    )}
                  </>
                ) : (
                  <>
                    <h3 className="text-base font-medium text-black dark:text-white mb-2">
                      {suggestion.content}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Click to use this prompt
                    </p>
                  </>
                )}
              </button>
            ))}
          </div>

          {/* Message Input */}
          <div className="text-base font-normal md:max-w-3xl w-full py-3">
            <MessageInput
              prompt={prompt}
              onPromptChange={onPromptChange}
              onSubmit={onSubmit}
              placeholder="Ask K2 Anything...."
              selectedModels={selectedModels}
              files={[]}
              onFilesChange={() => {}}
              transparentBackground={transparentBackground}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
